"""
Performance optimization scripts for Windows Performance Optimizer
Specifically designed for PUBG Mobile emulator players
"""
import os
import gc
import psutil
import subprocess
import ctypes
from ctypes import wintypes
import winreg
from typing import Tuple, List
from utils import run_command, log_action, get_emulator_processes, set_registry_value


class PerformanceOptimizer:
    """Main class for performance optimization operations"""
    
    def __init__(self):
        self.kernel32 = ctypes.windll.kernel32
        self.ntdll = ctypes.windll.ntdll
    
    def clear_memory_cache(self) -> Tuple[bool, str]:
        """Clear system memory cache and reduce RAM usage"""
        try:
            # Force garbage collection
            gc.collect()
            
            # Clear standby memory using Windows API
            success = self._clear_standby_memory()
            
            # Clear working set for current process
            try:
                handle = self.kernel32.GetCurrentProcess()
                self.kernel32.SetProcessWorkingSetSize(handle, -1, -1)
            except:
                pass
            
            log_action("Clear Memory Cache", success, "Cleared system memory cache")
            return success, "Memory cache cleared successfully" if success else "Failed to clear memory cache"
            
        except Exception as e:
            log_action("Clear Memory Cache", False, str(e))
            return False, f"Error clearing memory cache: {str(e)}"
    
    def _clear_standby_memory(self) -> bool:
        """Clear standby memory using Windows API"""
        try:
            # Define constants
            SYSTEM_MEMORY_LIST_INFORMATION = 80
            MemoryPurgeStandbyList = 4
            
            # Try to clear standby memory
            status = self.ntdll.NtSetSystemInformation(
                SYSTEM_MEMORY_LIST_INFORMATION,
                ctypes.byref(ctypes.c_int(MemoryPurgeStandbyList)),
                ctypes.sizeof(ctypes.c_int)
            )
            return status == 0
        except:
            return False
    
    def clear_dns_cache(self) -> Tuple[bool, str]:
        """Clear DNS cache to improve network performance"""
        try:
            success, output, error = run_command("ipconfig /flushdns")
            
            if success and "successfully flushed" in output.lower():
                log_action("Clear DNS Cache", True, "DNS cache flushed")
                return True, "DNS cache cleared successfully"
            else:
                log_action("Clear DNS Cache", False, error)
                return False, f"Failed to clear DNS cache: {error}"
                
        except Exception as e:
            log_action("Clear DNS Cache", False, str(e))
            return False, f"Error clearing DNS cache: {str(e)}"
    
    def clear_temp_files(self) -> Tuple[bool, str]:
        """Clear temporary files to free up disk space"""
        try:
            temp_dirs = [
                os.environ.get('TEMP', ''),
                os.environ.get('TMP', ''),
                'C:\\Windows\\Temp',
                'C:\\Windows\\Prefetch'
            ]
            
            files_deleted = 0
            total_size = 0
            
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    deleted, size = self._clean_directory(temp_dir)
                    files_deleted += deleted
                    total_size += size
            
            # Clear browser caches
            browser_deleted, browser_size = self._clear_browser_caches()
            files_deleted += browser_deleted
            total_size += browser_size
            
            message = f"Deleted {files_deleted} files, freed {total_size / (1024*1024):.1f} MB"
            log_action("Clear Temp Files", True, message)
            return True, message
            
        except Exception as e:
            log_action("Clear Temp Files", False, str(e))
            return False, f"Error clearing temp files: {str(e)}"
    
    def _clean_directory(self, directory: str) -> Tuple[int, int]:
        """Clean files in a directory"""
        files_deleted = 0
        total_size = 0
        
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        if os.path.exists(file_path):
                            size = os.path.getsize(file_path)
                            os.remove(file_path)
                            files_deleted += 1
                            total_size += size
                    except:
                        continue
        except:
            pass
        
        return files_deleted, total_size
    
    def _clear_browser_caches(self) -> Tuple[int, int]:
        """Clear browser caches"""
        files_deleted = 0
        total_size = 0
        
        # Chrome cache
        chrome_cache = os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cache")
        if os.path.exists(chrome_cache):
            deleted, size = self._clean_directory(chrome_cache)
            files_deleted += deleted
            total_size += size
        
        # Firefox cache
        firefox_cache = os.path.expanduser("~\\AppData\\Local\\Mozilla\\Firefox\\Profiles")
        if os.path.exists(firefox_cache):
            for profile in os.listdir(firefox_cache):
                cache_dir = os.path.join(firefox_cache, profile, "cache2")
                if os.path.exists(cache_dir):
                    deleted, size = self._clean_directory(cache_dir)
                    files_deleted += deleted
                    total_size += size
        
        return files_deleted, total_size
    
    def set_high_performance_mode(self) -> Tuple[bool, str]:
        """Set Windows to high performance power plan"""
        try:
            # Get high performance GUID
            success, output, error = run_command("powercfg /list")
            
            if not success:
                return False, f"Failed to get power plans: {error}"
            
            # Look for high performance plan
            high_perf_guid = None
            for line in output.split('\n'):
                if 'high performance' in line.lower():
                    # Extract GUID
                    parts = line.split()
                    for part in parts:
                        if len(part) == 36 and part.count('-') == 4:
                            high_perf_guid = part
                            break
                    break
            
            if not high_perf_guid:
                # Create high performance plan if not found
                success, output, error = run_command("powercfg /duplicatescheme 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c")
                if success:
                    high_perf_guid = "8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c"
            
            if high_perf_guid:
                success, output, error = run_command(f"powercfg /setactive {high_perf_guid}")
                if success:
                    log_action("Set High Performance", True, "High performance mode activated")
                    return True, "High performance mode activated"
            
            log_action("Set High Performance", False, "Could not activate high performance mode")
            return False, "Could not activate high performance mode"
            
        except Exception as e:
            log_action("Set High Performance", False, str(e))
            return False, f"Error setting high performance mode: {str(e)}"
    
    def optimize_network_settings(self) -> Tuple[bool, str]:
        """Optimize network settings for gaming"""
        try:
            optimizations = []
            
            # Reset network adapters
            success, _, _ = run_command("netsh winsock reset")
            if success:
                optimizations.append("Winsock reset")
            
            # Reset TCP/IP stack
            success, _, _ = run_command("netsh int ip reset")
            if success:
                optimizations.append("TCP/IP reset")
            
            # Flush ARP cache
            success, _, _ = run_command("arp -d *")
            if success:
                optimizations.append("ARP cache cleared")
            
            # Optimize TCP settings via registry
            tcp_optimizations = self._optimize_tcp_registry()
            optimizations.extend(tcp_optimizations)
            
            message = f"Network optimizations applied: {', '.join(optimizations)}"
            log_action("Optimize Network", True, message)
            return True, message
            
        except Exception as e:
            log_action("Optimize Network", False, str(e))
            return False, f"Error optimizing network: {str(e)}"
    
    def _optimize_tcp_registry(self) -> List[str]:
        """Optimize TCP settings in registry"""
        optimizations = []
        tcp_key = r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters"
        
        # TCP optimizations for gaming
        settings = {
            "TcpAckFrequency": 1,
            "TCPNoDelay": 1,
            "TcpDelAckTicks": 0,
            "DefaultTTL": 64
        }
        
        for setting, value in settings.items():
            if set_registry_value(tcp_key, setting, value):
                optimizations.append(f"TCP {setting}")
        
        return optimizations

    def kill_unnecessary_processes(self) -> Tuple[bool, str]:
        """Kill unnecessary processes to free up resources"""
        try:
            # Processes that are safe to kill and consume resources
            unnecessary_processes = [
                'chrome.exe', 'firefox.exe', 'edge.exe', 'opera.exe',  # Browsers
                'spotify.exe', 'discord.exe', 'skype.exe', 'teams.exe',  # Communication
                'steam.exe', 'epicgameslauncher.exe', 'origin.exe',  # Game launchers
                'obs64.exe', 'obs32.exe',  # Recording software
                'photoshop.exe', 'illustrator.exe',  # Adobe
                'code.exe', 'devenv.exe',  # IDEs
                'torrent.exe', 'utorrent.exe', 'bittorrent.exe'  # Torrents
            ]

            killed_processes = []
            emulator_processes = get_emulator_processes()

            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_name = proc.info['name'].lower()

                    # Don't kill emulator processes
                    if proc.info['name'] in emulator_processes:
                        continue

                    # Kill unnecessary processes
                    if any(unnecessary in proc_name for unnecessary in unnecessary_processes):
                        proc.terminate()
                        killed_processes.append(proc.info['name'])

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            message = f"Killed {len(killed_processes)} unnecessary processes"
            if killed_processes:
                message += f": {', '.join(killed_processes[:5])}"
                if len(killed_processes) > 5:
                    message += f" and {len(killed_processes) - 5} more"

            log_action("Kill Unnecessary Processes", True, message)
            return True, message

        except Exception as e:
            log_action("Kill Unnecessary Processes", False, str(e))
            return False, f"Error killing processes: {str(e)}"

    def optimize_emulator_priority(self) -> Tuple[bool, str]:
        """Set high priority for emulator processes"""
        try:
            emulator_names = get_emulator_processes()
            optimized_emulators = []

            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'] in emulator_names:
                        # Set high priority
                        proc.nice(psutil.HIGH_PRIORITY_CLASS)
                        optimized_emulators.append(proc.info['name'])

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if optimized_emulators:
                message = f"Set high priority for: {', '.join(optimized_emulators)}"
                log_action("Optimize Emulator Priority", True, message)
                return True, message
            else:
                message = "No emulator processes found to optimize"
                log_action("Optimize Emulator Priority", False, message)
                return False, message

        except Exception as e:
            log_action("Optimize Emulator Priority", False, str(e))
            return False, f"Error optimizing emulator priority: {str(e)}"

    def disable_windows_game_mode(self) -> Tuple[bool, str]:
        """Disable Windows Game Mode which can interfere with emulators"""
        try:
            game_mode_key = r"SOFTWARE\Microsoft\GameBar"

            # Disable Game Mode
            success1 = set_registry_value(game_mode_key, "AllowAutoGameMode", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER)
            success2 = set_registry_value(game_mode_key, "AutoGameModeEnabled", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER)

            # Disable Game Bar
            success3 = set_registry_value(game_mode_key, "UseNexusForGameBarEnabled", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER)

            if success1 or success2 or success3:
                message = "Windows Game Mode disabled"
                log_action("Disable Game Mode", True, message)
                return True, message
            else:
                message = "Failed to disable Game Mode"
                log_action("Disable Game Mode", False, message)
                return False, message

        except Exception as e:
            log_action("Disable Game Mode", False, str(e))
            return False, f"Error disabling Game Mode: {str(e)}"

    def empty_recycle_bin(self) -> Tuple[bool, str]:
        """Empty the recycle bin"""
        try:
            success, output, error = run_command("rd /s /q C:\\$Recycle.Bin")

            # Alternative method using PowerShell
            if not success:
                success, output, error = run_command(
                    "powershell.exe -Command \"Clear-RecycleBin -Force\""
                )

            if success:
                message = "Recycle bin emptied"
                log_action("Empty Recycle Bin", True, message)
                return True, message
            else:
                message = f"Failed to empty recycle bin: {error}"
                log_action("Empty Recycle Bin", False, message)
                return False, message

        except Exception as e:
            log_action("Empty Recycle Bin", False, str(e))
            return False, f"Error emptying recycle bin: {str(e)}"

    def optimize_virtual_memory(self) -> Tuple[bool, str]:
        """Optimize virtual memory settings"""
        try:
            # Get total RAM
            total_ram_gb = psutil.virtual_memory().total / (1024**3)

            # Calculate optimal page file size (1.5x RAM)
            optimal_size_mb = int(total_ram_gb * 1.5 * 1024)

            # Set virtual memory via registry
            vm_key = r"SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management"

            # Disable automatic page file management
            success1 = set_registry_value(vm_key, "DisablePagingExecutive", 1)

            # Set page file size
            success2 = set_registry_value(vm_key, "PagingFiles", f"C:\\pagefile.sys {optimal_size_mb} {optimal_size_mb}", winreg.REG_MULTI_SZ)

            if success1 or success2:
                message = f"Virtual memory optimized (Page file: {optimal_size_mb} MB)"
                log_action("Optimize Virtual Memory", True, message)
                return True, message
            else:
                message = "Failed to optimize virtual memory"
                log_action("Optimize Virtual Memory", False, message)
                return False, message

        except Exception as e:
            log_action("Optimize Virtual Memory", False, str(e))
            return False, f"Error optimizing virtual memory: {str(e)}"

    def run_full_optimization(self, profile: str = "pubg_mobile") -> Tuple[bool, List[str]]:
        """Run full optimization based on profile"""
        results = []

        optimization_functions = {
            "clear_memory": self.clear_memory_cache,
            "clear_dns": self.clear_dns_cache,
            "clear_temp_files": self.clear_temp_files,
            "set_high_performance": self.set_high_performance_mode,
            "optimize_network": self.optimize_network_settings,
            "kill_unnecessary_processes": self.kill_unnecessary_processes,
            "optimize_emulator_priority": self.optimize_emulator_priority,
            "disable_game_mode": self.disable_windows_game_mode,
            "empty_recycle_bin": self.empty_recycle_bin,
            "clear_standby_memory": self.clear_memory_cache,
            "optimize_virtual_memory": self.optimize_virtual_memory
        }

        # Default optimizations for PUBG Mobile
        if profile == "pubg_mobile":
            optimizations_to_run = [
                "clear_memory", "clear_dns", "clear_temp_files",
                "set_high_performance", "optimize_network",
                "kill_unnecessary_processes", "optimize_emulator_priority",
                "disable_game_mode"
            ]
        elif profile == "basic":
            optimizations_to_run = [
                "clear_temp_files", "clear_dns", "empty_recycle_bin"
            ]
        else:  # gaming profile
            optimizations_to_run = [
                "clear_memory", "clear_dns", "clear_temp_files",
                "set_high_performance", "optimize_network",
                "kill_unnecessary_processes"
            ]

        success_count = 0
        for optimization in optimizations_to_run:
            if optimization in optimization_functions:
                try:
                    success, message = optimization_functions[optimization]()
                    results.append(f"{'✓' if success else '✗'} {message}")
                    if success:
                        success_count += 1
                except Exception as e:
                    results.append(f"✗ Error in {optimization}: {str(e)}")

        overall_success = success_count > len(optimizations_to_run) // 2
        log_action("Full Optimization", overall_success, f"Completed {success_count}/{len(optimizations_to_run)} optimizations")

        return overall_success, results

    def optimize_registry_for_gaming(self) -> Tuple[bool, str]:
        """Optimize Windows registry settings for gaming performance"""
        try:
            optimizations = []

            # Disable Windows Update automatic restart
            success1 = set_registry_value(
                r"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU",
                "NoAutoRebootWithLoggedOnUsers", 1, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success1:
                optimizations.append("Disabled automatic Windows Update restart")

            # Optimize visual effects for performance
            success2 = set_registry_value(
                r"Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects",
                "VisualFXSetting", 2, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER
            )
            if success2:
                optimizations.append("Set visual effects for best performance")

            # Optimize memory management
            success3 = set_registry_value(
                r"SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management",
                "LargeSystemCache", 0, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success3:
                optimizations.append("Optimized memory management for applications")

            if optimizations:
                message = f"Registry optimized: {', '.join(optimizations)}"
                log_action("Registry Optimization", True, message)
                return True, message
            else:
                message = "No registry optimizations could be applied"
                log_action("Registry Optimization", False, message)
                return False, message

        except Exception as e:
            log_action("Registry Optimization", False, str(e))
            return False, f"Error optimizing registry: {str(e)}"

    def optimize_gpu_settings(self) -> Tuple[bool, str]:
        """Optimize GPU settings for gaming performance"""
        try:
            optimizations = []

            # Set GPU scheduling to hardware accelerated
            success1 = set_registry_value(
                r"SYSTEM\CurrentControlSet\Control\GraphicsDrivers",
                "HwSchMode", 2, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success1:
                optimizations.append("Enabled hardware-accelerated GPU scheduling")

            if optimizations:
                message = f"GPU optimized: {', '.join(optimizations)}"
                log_action("GPU Optimization", True, message)
                return True, message
            else:
                message = "GPU settings checked (no changes needed)"
                log_action("GPU Optimization", True, message)
                return True, message

        except Exception as e:
            log_action("GPU Optimization", False, str(e))
            return False, f"Error optimizing GPU: {str(e)}"

    def optimize_cpu_priority(self) -> Tuple[bool, str]:
        """Optimize CPU priority settings for gaming"""
        try:
            optimizations = []

            # Set processor scheduling for programs (not background services)
            success1 = set_registry_value(
                r"SYSTEM\CurrentControlSet\Control\PriorityControl",
                "Win32PrioritySeparation", 38, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success1:
                optimizations.append("Set CPU priority for foreground applications")

            if optimizations:
                message = f"CPU optimized: {', '.join(optimizations)}"
                log_action("CPU Optimization", True, message)
                return True, message
            else:
                message = "CPU settings checked (no changes needed)"
                log_action("CPU Optimization", True, message)
                return True, message

        except Exception as e:
            log_action("CPU Optimization", False, str(e))
            return False, f"Error optimizing CPU: {str(e)}"

    def advanced_memory_optimization(self) -> Tuple[bool, str]:
        """Advanced memory optimization for gaming"""
        try:
            optimizations = []

            # Clear recycle bin first
            success1, _ = self.empty_recycle_bin()
            if success1:
                optimizations.append("Cleared recycle bin")

            # Optimize paging file
            success2 = set_registry_value(
                r"SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management",
                "ClearPageFileAtShutdown", 1, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success2:
                optimizations.append("Enabled page file clearing at shutdown")

            # Disable superfetch for SSDs
            success3 = set_registry_value(
                r"SYSTEM\CurrentControlSet\Services\SysMain",
                "Start", 4, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success3:
                optimizations.append("Disabled Superfetch service")

            # Force memory cleanup
            success4, _ = self.clear_memory_cache()
            if success4:
                optimizations.append("Forced memory cleanup")

            if optimizations:
                message = f"Memory optimized: {', '.join(optimizations)}"
                log_action("Advanced Memory Optimization", True, message)
                return True, message
            else:
                message = "Memory optimization completed"
                log_action("Advanced Memory Optimization", True, message)
                return True, message

        except Exception as e:
            log_action("Advanced Memory Optimization", False, str(e))
            return False, f"Error in memory optimization: {str(e)}"
