"""
Modern Styling System for AbuSaker Tools
PUBG Mobile Emulator Performance Optimizer
Developed by <PERSON><PERSON> Damra
"""

import tkinter as tk
from tkinter import ttk

class ModernTheme:
    """PUBG-inspired theme for AbuSaker Tools - Based on external UI design"""

    # PUBG-inspired color palette from external UI
    COLORS = {
        # Primary colors (from external UI)
        'primary': '#FF6B35',           # PUBG Orange
        'primary_dark': '#E55A2B',      # Darker orange
        'secondary': '#F7931E',         # Golden orange
        'accent': '#FFD23F',            # Bright yellow
        'accent_light': '#c7fff6',      # Light cyan for checked states (from external UI)

        # Background colors (from external UI)
        'bg_primary': '#1A1A1A',        # Dark background
        'bg_secondary': '#2D2D2D',      # Lighter dark
        'bg_tertiary': '#3D3D3D',       # Card background
        'bg_hover': '#4D4D4D',          # Hover state
        'bg_transparent': 'rgba(0, 0, 0, 0)',  # Transparent

        # Text colors (from external UI)
        'text_primary': '#FFFFFF',      # White text
        'text_secondary': '#CCCCCC',    # Light gray
        'text_muted': '#969696',        # Muted gray (from external UI)
        'text_accent': '#FF6B35',       # Accent text
        'text_black': '#000000',        # Black text

        # Status colors
        'success': '#4CAF50',           # Green
        'warning': '#FF9800',           # Orange
        'error': '#F44336',             # Red
        'info': '#2196F3',              # Blue
        'gameloop_connected': '#00AA00', # Green for connected state

        # Border colors
        'border': '#555555',            # Border gray
        'border_light': '#777777',      # Lighter border
        'border_accent': '#FF6B35',     # Accent border
        'border_black': '#000000',      # Black border

        # Special colors from external UI
        'disabled_text': 'rgb(80, 80, 80)',
        'disabled_bg': 'rgba(6, 6, 6, 200)',
        'menu_bg': 'rgb(85, 85, 85)',
        'force_close_bg': 'rgba(255, 0, 4, 50)',
    }
    
    # Typography (Agency FB from external UI)
    FONTS = {
        'title': ('Agency FB', 35, 'bold'),        # Main title
        'heading': ('Agency FB', 23, 'bold'),      # Section headings
        'subheading': ('Agency FB', 20, 'bold'),   # Sub headings
        'body': ('Agency FB', 16, 'bold'),         # Body text
        'small': ('Agency FB', 12, 'bold'),        # Small text
        'button': ('Agency FB', 20, 'bold'),       # Button text
        'status': ('Agency FB', 19, 'bold'),       # Status text
        'mono': ('Consolas', 9),                   # Monospace fallback
    }
    
    # Spacing
    SPACING = {
        'xs': 4,
        'sm': 8,
        'md': 12,
        'lg': 16,
        'xl': 24,
        'xxl': 32,
    }
    
    def __init__(self, root):
        self.root = root
        self.style = ttk.Style()
        self._setup_theme()
    
    def _setup_theme(self):
        """Setup the modern theme"""
        # Configure root window
        self.root.configure(bg=self.COLORS['bg_primary'])
        
        # Use a modern theme as base
        try:
            self.style.theme_use('clam')
        except:
            self.style.theme_use('default')
        
        # Configure styles
        self._configure_labels()
        self._configure_buttons()
        self._configure_frames()
        self._configure_progressbars()
        self._configure_combobox()
        self._configure_checkbutton()
        self._configure_separator()
    
    def _configure_labels(self):
        """Configure label styles"""
        # Title label
        self.style.configure('Title.TLabel',
            font=self.FONTS['title'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['bg_primary']
        )
        
        # Heading label
        self.style.configure('Heading.TLabel',
            font=self.FONTS['heading'],
            foreground=self.COLORS['primary'],
            background=self.COLORS['bg_primary']
        )
        
        # Subheading label
        self.style.configure('Subheading.TLabel',
            font=self.FONTS['subheading'],
            foreground=self.COLORS['text_secondary'],
            background=self.COLORS['bg_primary']
        )
        
        # Body label
        self.style.configure('Body.TLabel',
            font=self.FONTS['body'],
            foreground=self.COLORS['text_secondary'],
            background=self.COLORS['bg_primary']
        )
        
        # Status labels
        self.style.configure('Status.TLabel',
            font=self.FONTS['body'],
            foreground=self.COLORS['accent'],
            background=self.COLORS['bg_primary']
        )
        
        # Developer label
        self.style.configure('Developer.TLabel',
            font=self.FONTS['small'],
            foreground=self.COLORS['text_muted'],
            background=self.COLORS['bg_primary']
        )
    
    def _configure_buttons(self):
        """Configure button styles - Based on external UI design"""
        # Primary button (like submit button in external UI)
        self.style.configure('Primary.TButton',
            font=self.FONTS['button'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['primary'],
            borderwidth=2,
            relief='flat',
            focuscolor='none',
            padding=(self.SPACING['lg'], self.SPACING['md'])
        )

        self.style.map('Primary.TButton',
            background=[('active', self.COLORS['primary_dark']),
                       ('pressed', self.COLORS['primary_dark'])],
            foreground=[('active', self.COLORS['text_primary']),
                       ('pressed', self.COLORS['accent_light'])]
        )

        # Secondary button (like fps buttons in external UI)
        self.style.configure('Secondary.TButton',
            font=self.FONTS['button'],
            foreground=self.COLORS['text_muted'],
            background=self.COLORS['bg_secondary'],
            borderwidth=2,
            relief='flat',
            focuscolor='none',
            padding=(self.SPACING['md'], self.SPACING['sm'])
        )

        self.style.map('Secondary.TButton',
            background=[('active', self.COLORS['bg_hover']),
                       ('pressed', self.COLORS['primary']),
                       ('selected', self.COLORS['primary'])],
            foreground=[('active', self.COLORS['text_primary']),
                       ('pressed', self.COLORS['accent_light']),
                       ('selected', self.COLORS['accent_light'])]
        )

        # Danger button (like force close in external UI)
        self.style.configure('Danger.TButton',
            font=self.FONTS['button'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['force_close_bg'],
            borderwidth=2,
            relief='flat',
            focuscolor='none',
            padding=(self.SPACING['md'], self.SPACING['sm'])
        )

        # Success button (like gameloop connected in external UI)
        self.style.configure('Success.TButton',
            font=self.FONTS['button'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['success'],
            borderwidth=2,
            relief='flat',
            focuscolor='none',
            padding=(self.SPACING['md'], self.SPACING['sm'])
        )
        
        self.style.map('Secondary.TButton',
            background=[('active', self.COLORS['bg_hover']),
                       ('pressed', self.COLORS['bg_hover'])],
            bordercolor=[('active', self.COLORS['border_accent'])]
        )
        
        # Accent button (for one-click optimization)
        self.style.configure('Accent.TButton',
            font=self.FONTS['heading'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['secondary'],
            borderwidth=0,
            focuscolor='none',
            padding=(self.SPACING['lg'], self.SPACING['md'])
        )
        
        self.style.map('Accent.TButton',
            background=[('active', self.COLORS['accent']),
                       ('pressed', self.COLORS['accent'])]
        )
    
    def _configure_frames(self):
        """Configure frame styles"""
        # Main frame
        self.style.configure('Main.TFrame',
            background=self.COLORS['bg_primary'],
            borderwidth=0
        )
        
        # Card frame
        self.style.configure('Card.TFrame',
            background=self.COLORS['bg_secondary'],
            borderwidth=1,
            relief='solid'
        )
        
        # Label frame
        self.style.configure('Modern.TLabelframe',
            background=self.COLORS['bg_secondary'],
            borderwidth=1,
            relief='solid',
            bordercolor=self.COLORS['border']
        )
        
        self.style.configure('Modern.TLabelframe.Label',
            font=self.FONTS['subheading'],
            foreground=self.COLORS['primary'],
            background=self.COLORS['bg_secondary']
        )
    
    def _configure_progressbars(self):
        """Configure progress bar styles"""
        self.style.configure('Modern.Horizontal.TProgressbar',
            background=self.COLORS['primary'],
            troughcolor=self.COLORS['bg_tertiary'],
            borderwidth=0,
            lightcolor=self.COLORS['primary'],
            darkcolor=self.COLORS['primary']
        )
    
    def _configure_combobox(self):
        """Configure combobox styles"""
        self.style.configure('Modern.TCombobox',
            font=self.FONTS['body'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['bg_tertiary'],
            fieldbackground=self.COLORS['bg_tertiary'],
            borderwidth=1,
            bordercolor=self.COLORS['border']
        )
        
        self.style.map('Modern.TCombobox',
            bordercolor=[('active', self.COLORS['border_accent']),
                        ('focus', self.COLORS['border_accent'])]
        )
    
    def _configure_checkbutton(self):
        """Configure checkbutton styles"""
        self.style.configure('Modern.TCheckbutton',
            font=self.FONTS['body'],
            foreground=self.COLORS['text_secondary'],
            background=self.COLORS['bg_secondary'],
            focuscolor='none'
        )
        
        self.style.map('Modern.TCheckbutton',
            foreground=[('active', self.COLORS['text_primary'])]
        )
    
    def _configure_separator(self):
        """Configure separator styles"""
        self.style.configure('Modern.TSeparator',
            background=self.COLORS['border']
        )
    
    def get_color(self, color_name):
        """Get color value by name"""
        return self.COLORS.get(color_name, '#FFFFFF')
    
    def get_font(self, font_name):
        """Get font by name"""
        return self.FONTS.get(font_name, ('Segoe UI', 10))
    
    def get_spacing(self, spacing_name):
        """Get spacing value by name"""
        return self.SPACING.get(spacing_name, 8)

def apply_modern_theme(root):
    """Apply modern theme to the application"""
    return ModernTheme(root)
